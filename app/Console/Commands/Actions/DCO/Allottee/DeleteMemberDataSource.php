<?php

namespace App\Console\Commands\Actions\DCO\Allottee;

use Illuminate\Console\Command;
use App\Console\Commands\Action;

class DeleteMemberDataSource extends Action
{
    protected $signature = 'datasource:deleteMember {flowId} {parentId} {input}';

    protected $description = 'Delete Member DataSource';

    public function apply()
    {
        $id = $this->input['id'];

        $cancleDate = $this->input['cancelDate'] ?? date('d-m-Y');
        $cancellationReason = $this->input['cancellationReason'] ?? 'Member Cancelled';

        $obj = $this->tenantDB()->table('chsone_members_master AS member_master')
            ->where('member_master.id', $id)
            ->update([
                'member_master.status' => 0,
                'member_master.cancel_date' => $cancleDate
            ]);

        if($obj){
            $this->status = 'success';
            $this->message = 'Member Deactivated Successfully';
            $this->statusCode = 200;
            $this->data = [];
        }
        else{
            $this->status = 'error';
            $this->message = 'Member Deactivation Failed';
            $this->statusCode = 400;
            $this->data = [];
        }

    }
}