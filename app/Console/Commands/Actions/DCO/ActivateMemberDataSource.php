<?php

namespace App\Console\Commands\Actions\DCO;

use App\Console\Commands\Action;

class ActivateMemberDataSource extends Action
{
    protected $signature = 'datasource:activateMember {flowId} {parentId} {input}';

    protected $description = 'Activate Member DataSource';

    public function apply()
    {
        $member_id = $this->input['member_id'];

        // Validate that member exists
        $member = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $member_id)
            ->first();

        if (!$member) {
            $this->status = 'error';
            $this->message = 'Member not found';
            $this->statusCode = 404;
            $this->data = [];
            return;
        }

        // Update member status to active (1)
        $obj = $this->tenantDB()->table('chsone_members_master')
            ->where('id', $member_id)
            ->update([
                'status' => 1, // 1 for active
                'updated_date' => now(),
                'updated_by' => $this->input['user_id'] ?? null
            ]);

        if ($obj) {
            $this->status = 'success';
            $this->message = 'Member activated successfully';
            $this->statusCode = 200;
            $this->data = [
                'member_id' => $member_id,
                'status' => 'active'
            ];
        } else {
            $this->status = 'error';
            $this->message = 'Failed to activate member';
            $this->statusCode = 400;
            $this->data = [];
        }
    }
}
